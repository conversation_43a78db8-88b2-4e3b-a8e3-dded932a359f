import Image from 'next/image';

export default function Home() {
  return (
    <main className="min-h-screen">
      {/* 헤더 섹션 */}
      <header className="fixed top-0 left-0 right-0 bg-background/90 backdrop-blur-sm z-50 py-4">
        <div className="container mx-auto flex justify-between items-center px-4">
          <div className="text-2xl font-serif font-bold text-primary">
            빵답게
          </div>
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="hover:text-primary transition-colors">홈</a>
            <a href="#" className="hover:text-primary transition-colors">메뉴</a>
            <a href="#" className="hover:text-primary transition-colors">배송안내</a>
            <a href="#" className="hover:text-primary transition-colors">리뷰</a>
            <a href="#" className="hover:text-primary transition-colors">문의</a>
          </nav>
          <div className="flex space-x-4 items-center">
            <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-full transition-colors">
              주문하기
            </button>
          </div>
        </div>
      </header>

      {/* 히어로 섹션 */}
      <section className="relative h-screen flex items-center">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-bread.jpg"
            alt="신선한 빵"
            fill
            style={{ objectFit: 'cover' }}
            priority
          />
          <div className="absolute inset-0 bg-black/40" />
        </div>
        <div className="container mx-auto px-4 z-10">
          <div className="max-w-2xl">
            <h1 className="text-5xl md:text-6xl font-serif font-bold text-white mb-6">
              이 사이트의 브래드는,<br />
              <span className="text-primary">빵답게</span>
            </h1>
            <p className="text-xl text-white/90 mb-8">
              갓 구운 신선한 빵을 제조사에서 직접 배송해드립니다.<br />
              프리미엄 베이커리의 맛을 집에서 경험하세요.
            </p>
            <div className="flex space-x-4">
              <button className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-full font-semibold transition-all hover:shadow-lg">
                메뉴 보기
              </button>
              <button className="bg-transparent border-2 border-white text-white px-6 py-3 rounded-full font-semibold transition-all hover:bg-white hover:text-foreground">
                더 알아보기
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* 브랜드 가치 섹션 */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif font-bold mb-4">
              신선한 빵을 직접 배송합니다
            </h2>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto">
              빵답게는 최고급 재료로 매일 아침 갓 구운 빵을 제공합니다.
              어떤 첨가물도 없이 순수한 맛을 즐기세요.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            <div className="bg-white rounded-lg p-8 shadow-lg transform transition-all hover:scale-105">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">최고급 재료</h3>
              <p className="text-foreground/70 text-center">
                엄선된 고품질 재료만을 사용하여 건강하고 맛있는 빵을 매일 제공합니다.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-8 shadow-lg transform transition-all hover:scale-105">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">매일 아침 제작</h3>
              <p className="text-foreground/70 text-center">
                매일 아침 제과사들이 직접 제작하는 신선한 빵을 당일 배송해드립니다.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-8 shadow-lg transform transition-all hover:scale-105">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6 mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-center mb-4">직접 배송</h3>
              <p className="text-foreground/70 text-center">
                중간 유통 과정 없이 제조사에서 고객님께 직접 배송하여 최고의 신선도를 보장합니다.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 베스트셀러 섹션 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif font-bold mb-4">
              인기 메뉴
            </h2>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto">
              빵답게의 가장 사랑받는 메뉴를 소개합니다.
              매일 아침 갓 구운 빵을 직접 배송해드립니다.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* 상품 1 */}
            <div className="bg-background rounded-lg overflow-hidden shadow-lg transition-all hover:shadow-xl">
              <div className="relative h-64">
                <Image 
                  src="/images/product-1.jpg" 
                  alt="크로아상" 
                  fill 
                  style={{ objectFit: 'cover' }} 
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">크로아상</h3>
                <p className="text-foreground/70 mb-4">바삭하고 겹이 많은 프리미엄 크로아상</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">4,500원</span>
                  <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-full text-sm transition-colors">
                    장바구니
                  </button>
                </div>
              </div>
            </div>
            
            {/* 상품 2 */}
            <div className="bg-background rounded-lg overflow-hidden shadow-lg transition-all hover:shadow-xl">
              <div className="relative h-64">
                <Image 
                  src="/images/product-2.jpg" 
                  alt="바게트" 
                  fill 
                  style={{ objectFit: 'cover' }} 
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">바게트</h3>
                <p className="text-foreground/70 mb-4">바삭한 겉바깥과 부드러운 속을 가진 프랑스 바게트</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">3,800원</span>
                  <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-full text-sm transition-colors">
                    장바구니
                  </button>
                </div>
              </div>
            </div>
            
            {/* 상품 3 */}
            <div className="bg-background rounded-lg overflow-hidden shadow-lg transition-all hover:shadow-xl">
              <div className="relative h-64">
                <Image 
                  src="/images/product-3.jpg" 
                  alt="생크림 빵" 
                  fill 
                  style={{ objectFit: 'cover' }} 
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">생크림 빵</h3>
                <p className="text-foreground/70 mb-4">새로운 크림을 사용한 부드러운 생크림 빵</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">5,200원</span>
                  <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-full text-sm transition-colors">
                    장바구니
                  </button>
                </div>
              </div>
            </div>
            
            {/* 상품 4 */}
            <div className="bg-background rounded-lg overflow-hidden shadow-lg transition-all hover:shadow-xl">
              <div className="relative h-64">
                <Image 
                  src="/images/product-4.jpg" 
                  alt="소세지 빵" 
                  fill 
                  style={{ objectFit: 'cover' }} 
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">소세지 빵</h3>
                <p className="text-foreground/70 mb-4">프리미엄 소세지를 넣어 구운 풍미 넘치는 빵</p>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold text-primary">4,800원</span>
                  <button className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-full text-sm transition-colors">
                    장바구니
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <button className="bg-transparent border-2 border-primary text-primary hover:bg-primary hover:text-white px-6 py-3 rounded-full font-semibold transition-all">
              전체 메뉴 보기
            </button>
          </div>
        </div>
      </section>

      {/* 배송 과정 섹션 */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif font-bold mb-4">
              신선 배송 프로세스
            </h2>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto">
              빵답게는 가장 신선한 빵을 고객님께 전달하기 위해 모든 과정을 직접 관리합니다.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto text-white font-bold text-xl">
                1
              </div>
              <h3 className="text-xl font-bold mb-4">아침 제작</h3>
              <p className="text-foreground/70">
                매일 아침 5시부터 신선한 재료로 빵을 제작합니다.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto text-white font-bold text-xl">
                2
              </div>
              <h3 className="text-xl font-bold mb-4">품질 검사</h3>
              <p className="text-foreground/70">
                엄격한 품질 검사를 통과한 제품만 배송됩니다.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto text-white font-bold text-xl">
                3
              </div>
              <h3 className="text-xl font-bold mb-4">신선 포장</h3>
              <p className="text-foreground/70">
                특수 설계된 포장으로 신선도와 맛을 유지합니다.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mb-6 mx-auto text-white font-bold text-xl">
                4
              </div>
              <h3 className="text-xl font-bold mb-4">당일 배송</h3>
              <p className="text-foreground/70">
                주문 후 당일 배송으로 가장 신선한 상태로 전달됩니다.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 고객 리뷰 섹션 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-serif font-bold mb-4">
              고객 리뷰
            </h2>
            <p className="text-lg text-foreground/70 max-w-3xl mx-auto">
              빵답게를 경험한 고객님들의 생생한 후기입니다.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-background p-8 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4">
                  <span className="text-primary font-bold">김</span>
                </div>
                <div>
                  <h4 className="font-bold">김서연</h4>
                  <div className="flex text-primary">
                    <span>★★★★★</span>
                  </div>
                </div>
              </div>
              <p className="text-foreground/80 italic">
                "매일 아침 빵답게의 크로아상으로 하루를 시작합니다. 바삭한 겉면과 부드러운 속은 정말 다른 빵집과 비교가 안 됩니다. 배송도 항상 정확해요!"
              </p>
            </div>
            
            <div className="bg-background p-8 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4">
                  <span className="text-primary font-bold">이</span>
                </div>
                <div>
                  <h4 className="font-bold">이준호</h4>
                  <div className="flex text-primary">
                    <span>★★★★★</span>
                  </div>
                </div>
              </div>
              <p className="text-foreground/80 italic">
                "바게트 퀄리티가 정말 놀랍습니다. 프랑스 현지에서 먹던 맛이 나요. 신선도가 다른 곳과 확실히 차이가 납니다. 앞으로도 계속 주문할 예정입니다."
              </p>
            </div>
            
            <div className="bg-background p-8 rounded-lg shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4">
                  <span className="text-primary font-bold">박</span>
                </div>
                <div>
                  <h4 className="font-bold">박민지</h4>
                  <div className="flex text-primary">
                    <span>★★★★★</span>
                  </div>
                </div>
              </div>
              <p className="text-foreground/80 italic">
                "아이들 간식으로 주문했는데, 첨가물 없는 건강한 빵이라 안심하고 먹일 수 있어요. 소세지 빵이 특히 인기가 좋습니다. 정기 배송 서비스도 편리해요."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA 섹션 */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-serif font-bold text-white mb-6">
            지금 바로 신선한 빵을 만나보세요
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            매일 아침 갓 구운 빵을 당일 배송해 드립니다.
            프리미엄 베이커리의 맛을 집에서 경험하세요.
          </p>
          <button className="bg-white text-primary hover:bg-foreground hover:text-white px-8 py-4 rounded-full font-bold text-lg transition-all hover:shadow-lg">
            지금 주문하기
          </button>
        </div>
      </section>

      {/* 푸터 */}
      <footer className="bg-foreground text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-serif font-bold text-primary mb-4">빵답게</h3>
              <p className="text-white/70">
                신선한 빵을 직접 배송하는 프리미엄 베이커리입니다.
              </p>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">메뉴</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-white/70 hover:text-primary">모든 제품</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">베스트셀러</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">신제품</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">선물세트</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">고객지원</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-white/70 hover:text-primary">배송 안내</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">자주 묻는 질문</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">교환 및 환불</a></li>
                <li><a href="#" className="text-white/70 hover:text-primary">문의하기</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-bold mb-4">연락처</h4>
              <p className="text-white/70 mb-2">서울시 강남구 베이커리로 123</p>
              <p className="text-white/70 mb-2">전화: 02-123-4567</p>
              <p className="text-white/70 mb-4">이메일: <EMAIL></p>
              <div className="flex space-x-4">
                <a href="#" className="text-white hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"/>
                  </svg>
                </a>
                <a href="#" className="text-white hover:text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.782 6.979 6.979 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/20 mt-12 pt-8 text-center text-white/50">
            &copy; 2025 빵답게. All rights reserved.
          </div>
        </div>
      </footer>
    </main>
  );
}
