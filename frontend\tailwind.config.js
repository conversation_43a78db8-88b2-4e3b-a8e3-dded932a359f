/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#D4AF37',
          dark: '#B89B2F',
        },
        secondary: {
          DEFAULT: '#8B5A2B',
          dark: '#704824',
        },
        accent: {
          DEFAULT: '#D4A76A',
          light: '#E0BF8F',
        },
        background: '#F5F3EF',
        foreground: '#1B1B1B',
      },
      fontFamily: {
        sans: ['Pretendard', 'Montserrat', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
    },
  },
  plugins: [],
};
