import './globals.css';
import { Metadata } from 'next';
import { Inter, Montserrat, Playfair_Display } from 'next/font/google';

const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
});

const playfair = Playfair_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair',
});

export const metadata: Metadata = {
  title: '빵답게 | 프리미엄 베이커리',
  description: '신선한 빵을 직접 배송해드립니다. 빵답게 - 이 사이트의 브래드는, 빵답게',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ko" className={`${montserrat.variable} ${playfair.variable}`}>
      <body className="bg-background text-foreground">
        {children}
      </body>
    </html>
  );
}
