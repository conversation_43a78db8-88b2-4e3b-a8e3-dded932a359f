# 베이커리 브랜드 웹사이트 통합 기획안 (최종 보완 포함)

---

## 1. 프로젝트 개요

- 사이트 목적: 수도권 200여 개 대형마트에서 이미 검증된 빵을, 소비자에게 직접 판매하는 브랜드 웹사이트 구축
- 브랜드 슬로건: “이 사이트의 브래드는, 빵답게”
- 브랜드 방향성: 정직한 공정, 현장 기반 신뢰, 감성적 고급 미니멀리즘
- 목표: 프론트 UX, 기능 흐름, 관리자 시스템, 기술사양까지 통합 설계

---

## 2. 브랜드 콘텐츠 구조

- SNS 후기가 아닌 오프라인 검증 (마트 진열, 시식 등 실물 인증 기반)
- 실물 후기 중심 시각 콘텐츠 구성
- 상시 운영 제품 / 주문제작 제품 / 기념일 이벤트 제품으로 라인업 분리
- 꽃집 연계 및 메시지 카드 포함한 선물/이벤트 UX 기획
- 기업 B2B 대량 주문 / 정기 납품 페이지 구성 포함

---

## 3. 전체 페이지 구성

### A. 콘텐츠/브랜드 중심 UX 페이지
1. 메인 페이지
2. 브랜드 소개
3. 제품 전체 보기
4. 제품 상세 페이지
5. 후기 / 실물 인증 갤러리
6. 제조/공정 소개
7. 오프라인 매장 안내
8. 상시 운영 제품 페이지
9. 주문 제작 제품 페이지
10. 기념일/꽃 연계 주문 페이지
11. 고객센터 / 자주 묻는 질문
12. 기업 제휴 (B2B) 안내
13. 브랜드 철학
14. 약관 / 정책 / 사업자 정보

### B. 사용자 기능 페이지
15. 회원가입 (소셜: 카카오/네이버/구글 + 일반: 휴대폰 인증)
16. 로그인
17. 비밀번호 재설정
18. 마이페이지 (주문 내역, 회원정보, 찜목록)
19. 장바구니 / 결제
20. 주문 완료 확인 페이지

### C. 운영자(관리자) 시스템 페이지
21. 관리자 로그인
22. 상품 관리
23. 주문 관리
24. 회원 관리
25. 후기 / 이미지 콘텐츠 관리
26. CMS 배너/이벤트/공지 관리
27. 통계 대시보드

---

## 4. 디자인 시스템 요약

- 컬러 시스템: #1B1B1B (배경), #D4AF37 (포인트), #F5F3EF (서브)
- 폰트: Pretendard, MaruBuri, Montserrat, Playfair Display
- 감성 고급 미니멀 스타일
- 모바일 퍼스트 / 반응형 설계
- 버튼, 슬라이드, 카드 등 컴포넌트 일관성 유지
- GSAP, Framer Motion 기반 인터랙션 계획
- 과도한 애니메이션 지양

---

## 5. PC vs 모바일 3D / 애니메이션 적용 전략

| 항목 | PC | 모바일 |
|------|----|--------|
| Hero 영상 | 고해상도 루프 영상 | 저용량 루프 or 정적 포스터 |
| 제품 애니메이션 | Spline / Three.js 회전형 | Lottie JSON 기반 단면 전환 |
| 인터랙션 | GSAP ScrollTrigger 등장/투명도/이동 | translateY + opacity 정도 |
| 공정 섹션 | 좌우 슬라이드 | 세로 스크롤 단계형 |
| 카드 hover | tilt / scale 효과 | 클릭 전환 중심 UX |

---

## 6. 참고용 3D / 애니메이션 사이트

- [Spline](https://spline.design) – 실시간 3D UI
- [Three.js Examples](https://threejs.org/examples/) – 고급 WebGL
- [LottieFiles](https://lottiefiles.com) – JSON 기반 경량 애니메이션
- [GSAP + Codepen](https://codepen.io/GreenSock) – 인터랙션/스크롤 효과
- [Framer Motion](https://www.framer.com/motion/) – React 기반 부드러운 모션

> MCP 연동 고려 시 위 사이트 예제 참조 가능

---

## 7. 기술 사양 (Tech Spec)

- **DB**: Supabase 또는 Firebase (사용자/주문/제품/권한 데이터)
- **인증**:
  - 소셜 로그인: 카카오, 네이버, 구글
  - 일반 로그인: 휴대폰 인증(SMS)
- **PG사**: Toss Payments (토스페이먼츠)
  - 직접 API 연동이 아닌, **결제 위젯 사용** 방식으로 간편 연동
- **개발 프레임워크**: React/Next.js (TypeScript) + Python (FastAPI 등)
- **MCP 연동 활용 가능**: 외부 애니메이션/3D 코드 즉시 반영

---

## 8. 추가 운영/기획 고려 요소 (보완 사항)

### 배송/물류 흐름
- 예약 주문 상품은 배송일 지정 옵션 제공
- 출고일 자동 계산 로직 고려 (예: "오늘 주문 → 이틀 후 출고")
- 기본 택배사: 우체국택배 등 연동 가능

### 마케팅 기능 구성
- 첫 구매 쿠폰, 이벤트용 소형 제품 동봉 가능성
- 구매 후기 등록 시 리워드 제공 (선택형 기획)
- 생일/기념일 알림 기반 타겟 마케팅 확장성 고려

### 정기배송 기능 여부
- 주 1회 / 월 2회 등 사용자가 선택 가능
- 정기 배송 제품 라인업은 별도 페이지 또는 설정 필요

### 기념일 주문 UX 강화
- 수령자 정보와 주문자 분리 입력
- 메시지 입력 필드, 선물 포장 옵션
- 배송일 사전 지정 필수

### CMS 콘텐츠 구분 체계
- 콘텐츠 유형: 공지사항 / 이벤트 / 배너 / 팝업 구분
- 노출 위치 / 우선순위 / 게시 기간 설정 필드 설계 필요

---

